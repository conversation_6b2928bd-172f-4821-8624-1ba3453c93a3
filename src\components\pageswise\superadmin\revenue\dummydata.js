// Mock data for revenue analytics
export const revenueData = [
  { month: 'Jan', revenue: 85000 },
  { month: 'Feb', revenue: 92000 },
  { month: 'Mar', revenue: 78000 },
  { month: 'Apr', revenue: 105000 },
  { month: 'May', revenue: 125000 },
  { month: 'Jun', revenue: 120000 },
  { month: 'Jul', revenue: 135000 },
  { month: 'Aug', revenue: 142000 },
  { month: 'Sep', revenue: 128000 },
  { month: 'Oct', revenue: 155000 },
  { month: 'Nov', revenue: 168000 },
  { month: 'Dec', revenue: 175000 },
];

export const paymentTypeData = [
  { name: 'Subscriptions', value: 750000, color: '#0D9488' },
  { name: 'Campaign Boosts', value: 350000, color: '#059669' },
  { name: 'Add-ons', value: 150000, color: '#10B981' },
];

export const transactionData = [
  {
    id: 'TXN1234',
    businessName: '<PERSON>',
    email: '<EMAIL>',
    paymentDate: '2024-07-18',
    amount: 500,
    paymentType: 'Subscription',
    status: 'Complete',
  },
  {
    id: 'TXN1890',
    businessName: 'Tech Solutions Inc.',
    email: '<EMAIL>',
    paymentDate: '2024-07-18',
    amount: 1200,
    paymentType: 'Campaign Boost',
    status: 'Complete',
  },
  {
    id: 'TXN1223',
    businessName: 'Liam Bennett',
    email: '<EMAIL>',
    paymentDate: '2024-07-17',
    amount: 750,
    paymentType: 'Add-on',
    status: 'Complete',
  },
  {
    id: 'TXN4556',
    businessName: 'Global Marketing Ltd.',
    email: '<EMAIL>',
    paymentDate: '2024-07-17',
    amount: 2500,
    paymentType: 'Subscription',
    status: 'Complete',
  },
  {
    id: 'TXN7889',
    businessName: 'Olivia Harper',
    email: '<EMAIL>',
    paymentDate: '2024-07-16',
    amount: 300,
    paymentType: 'Add-on',
    status: 'Complete',
  },
  {
    id: 'TXN8901',
    businessName: 'Creative Solutions',
    email: '<EMAIL>',
    paymentDate: '2024-07-16',
    amount: 800,
    paymentType: 'Campaign Boost',
    status: 'Complete',
  },
  {
    id: 'TXN2334',
    businessName: 'Ethan Walker',
    email: '<EMAIL>',
    paymentDate: '2024-07-15',
    amount: 1500,
    paymentType: 'Add-on',
    status: 'Complete',
  },
  {
    id: 'TXN8890',
    businessName: 'Innovative Solutions LLC',
    email: '<EMAIL>',
    paymentDate: '2024-07-15',
    amount: 500,
    paymentType: 'Add-on',
    status: 'Complete',
  },
  {
    id: 'TXN1224',
    businessName: 'Dynamic Enterprises',
    email: '<EMAIL>',
    paymentDate: '2024-07-14',
    amount: 1000,
    paymentType: 'Campaign Boost',
    status: 'Complete',
  },
  {
    id: 'TXN5567',
    businessName: 'NextGen Solutions',
    email: '<EMAIL>',
    paymentDate: '2024-07-13',
    amount: 2200,
    paymentType: 'Subscription',
    status: 'Complete',
  },
  {
    id: 'TXN9988',
    businessName: 'Digital Dynamics',
    email: '<EMAIL>',
    paymentDate: '2024-07-12',
    amount: 650,
    paymentType: 'Campaign Boost',
    status: 'Complete',
  },
  {
    id: 'TXN3344',
    businessName: 'Smart Enterprises',
    email: '<EMAIL>',
    paymentDate: '2024-07-11',
    amount: 1800,
    paymentType: 'Add-on',
    status: 'Complete',
  },
];

export const summaryMetrics = {
  totalRevenue: 1250000,
  monthlyRevenue: 120000,
  totalTransactions: 1247,
  pendingSettlements: 15000,
  growthRates: {
    revenue: 10,
    monthly: 5,
    transactions: 8,
    settlements: -2,
  },
};
