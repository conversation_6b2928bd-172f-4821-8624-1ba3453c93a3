// mockdata.tsx
export const mockWorkflows = [
  {
    id: 1,
    name: "Welcome Series",
    description: "Automated welcome sequence for new customers",
    status: "Active",
    trigger: "New Contact Added",
    totalRuns: 1250,
    completionRate: 85,
    createdAt: "2024-03-10",
    nodes: [
      {
        id: "start",
        type: "trigger",
        label: "New Contact Added",
        x: 100,
        y: 100,
      },
      { id: "delay1", type: "delay", label: "Wait 1 hour", x: 100, y: 200 },
      {
        id: "message1",
        type: "message",
        label: "Welcome Message",
        x: 100,
        y: 300,
      },
      { id: "delay2", type: "delay", label: "Wait 24 hours", x: 100, y: 400 },
      {
        id: "message2",
        type: "message",
        label: "Getting Started Guide",
        x: 100,
        y: 500,
      },
    ],
  },
  {
    id: 2,
    name: "Cart Abandonment",
    description: "Recover abandoned shopping carts",
    status: "Active",
    trigger: "Cart Abandoned",
    totalRuns: 890,
    completionRate: 67,
    createdAt: "2024-03-12",
    nodes: [
      { id: "start", type: "trigger", label: "Cart Abandoned", x: 100, y: 100 },
      { id: "delay1", type: "delay", label: "Wait 2 hours", x: 100, y: 200 },
      {
        id: "message1",
        type: "message",
        label: "Cart Reminder",
        x: 100,
        y: 300,
      },
      {
        id: "condition1",
        type: "condition",
        label: "Purchase Made?",
        x: 100,
        y: 400,
      },
      {
        id: "message2",
        type: "message",
        label: "Discount Offer",
        x: 200,
        y: 500,
      },
    ],
  },
  {
    id: 3,
    name: "Birthday Campaign",
    description: "Send birthday wishes and special offers",
    status: "Paused",
    trigger: "Birthday Date",
    totalRuns: 156,
    completionRate: 92,
    createdAt: "2024-03-08",
    nodes: [
      { id: "start", type: "trigger", label: "Birthday Date", x: 100, y: 100 },
      {
        id: "message1",
        type: "message",
        label: "Birthday Wishes",
        x: 100,
        y: 200,
      },
      { id: "tag1", type: "tag", label: "Add Birthday Tag", x: 100, y: 300 },
    ],
  },
];
