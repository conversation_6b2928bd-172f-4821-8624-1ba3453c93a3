/* ============= APP CONTAINER ============= */
.app-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;
  background: #f8fafc;
}

/* ============= MOBILE LAYOUT ============= */
.mobile-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Mobile Conversations View */
.mobile-conversations-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.mobile-header {
  flex-shrink: 0;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  height: 6%;
}

.mobile-search-section {
  flex-shrink: 0;
  padding: 16px;
  background: white;
}

.mobile-conversations-list {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Mobile Chat View */
.mobile-chat-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.mobile-chat-header {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.mobile-messages-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.mobile-messages-content {
  flex: 1;
  min-height: 0;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.mobile-input-section {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  position: relative;
}

.mobile-input-wrapper {
  padding: 12px;
}

.mobile-emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 16px;
  right: 16px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 12px;
  z-index: 30;
}

/* ============= DESKTOP LAYOUT ============= */
.desktop-layout {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

/* AdminSidebar Wrapper */
.admin-sidebar-wrapper {
  width: 260px;
  min-width: 260px;
  max-width: 260px;
  height: 100%;
  flex-shrink: 0;
  overflow: hidden;
}

/* Conversations Panel */
.conversations-panel {
  width: 350px;
  min-width: 350px;
  max-width: 350px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-right: 1px solid #e5e7eb;
  overflow: hidden;
}

.conversations-header {
  flex-shrink: 0;
  border-bottom: 1px solid #e5e7eb;
  background: white;
 margin-top: 9.3%;
}

.conversations-search {
  flex-shrink: 0;
  padding: 16px;
  background: white;
}

.conversations-list {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Chat Panel */
.chat-panel {
  flex: 1;
  min-width: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
  overflow: hidden;
}

.chat-header {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;

}

.chat-messages-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.chat-messages-content {
  flex: 1;
  min-height: 0;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.chat-input-section {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  position: relative;
}

.chat-input-wrapper {
  padding: 16px;
}

.desktop-emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 16px;
  right: 16px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 12px;
  z-index: 30;
}

/* ============= SCROLLBAR STYLES ============= */
*::-webkit-scrollbar {
  width: 6px;
}

*::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Hide textarea scrollbars */
.chat-textarea::-webkit-scrollbar {
  display: none;
}

.chat-textarea {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* ============= RESPONSIVE BREAKPOINTS ============= */
@media (max-width: 768px) {
  .desktop-layout {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .mobile-layout {
    display: none !important;
  }
}

/* ============= UTILITY CLASSES ============= */
.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.min-w-0 {
  min-width: 0 !important;
}

.flex-1 {
  flex: 1 1 0% !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-y-auto {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Message bubble styles */
.message-bubble {
  max-width: calc(100% - 80px);
}

@media (max-width: 640px) {
  .message-bubble {
    max-width: calc(100vw - 120px);
  }
}

/* Button styles */
.chat-button {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Global resets */
* {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  height: 100%;
}
